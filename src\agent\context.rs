use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tracing::debug;

/// Agent context that maintains state and environment information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AgentContext {
    /// Current working directory
    pub working_directory: PathBuf,
    /// Environment variables
    pub environment: HashMap<String, String>,
    /// Project information (if detected)
    pub project_info: Option<ProjectInfo>,
    /// User preferences
    pub user_preferences: UserPreferences,
    /// Session metadata
    pub session_metadata: SessionMetadata,
    /// Context history for maintaining state
    pub context_history: Vec<ContextEntry>,
}

/// Project information detected from the current directory
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProjectInfo {
    /// Project name
    pub name: String,
    /// Project type (e.g., "rust", "node", "python")
    pub project_type: ProjectType,
    /// Root directory of the project
    pub root_directory: PathBuf,
    /// Configuration files found
    pub config_files: Vec<PathBuf>,
    /// Dependencies detected
    pub dependencies: Vec<String>,
    /// Git information (if available)
    pub git_info: Option<GitInfo>,
}

/// Supported project types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ProjectType {
    Rust,
    Node,
    Python,
    Go,
    Java,
    CSharp,
    Cpp,
    Unknown,
}

impl ProjectType {
    /// Detect project type from directory contents
    pub fn detect(directory: &PathBuf) -> Self {
        if directory.join("Cargo.toml").exists() {
            ProjectType::Rust
        } else if directory.join("package.json").exists() {
            ProjectType::Node
        } else if directory.join("requirements.txt").exists() || directory.join("pyproject.toml").exists() {
            ProjectType::Python
        } else if directory.join("go.mod").exists() {
            ProjectType::Go
        } else if directory.join("pom.xml").exists() || directory.join("build.gradle").exists() {
            ProjectType::Java
        } else if directory.join("*.csproj").exists() || directory.join("*.sln").exists() {
            ProjectType::CSharp
        } else if directory.join("CMakeLists.txt").exists() || directory.join("Makefile").exists() {
            ProjectType::Cpp
        } else {
            ProjectType::Unknown
        }
    }

    /// Get the display name
    pub fn display_name(&self) -> &'static str {
        match self {
            ProjectType::Rust => "Rust",
            ProjectType::Node => "Node.js",
            ProjectType::Python => "Python",
            ProjectType::Go => "Go",
            ProjectType::Java => "Java",
            ProjectType::CSharp => "C#",
            ProjectType::Cpp => "C++",
            ProjectType::Unknown => "Unknown",
        }
    }

    /// Get common file extensions for this project type
    pub fn file_extensions(&self) -> Vec<&'static str> {
        match self {
            ProjectType::Rust => vec!["rs", "toml"],
            ProjectType::Node => vec!["js", "ts", "json", "jsx", "tsx"],
            ProjectType::Python => vec!["py", "pyx", "pyi", "toml", "txt"],
            ProjectType::Go => vec!["go", "mod", "sum"],
            ProjectType::Java => vec!["java", "xml", "gradle", "properties"],
            ProjectType::CSharp => vec!["cs", "csproj", "sln", "config"],
            ProjectType::Cpp => vec!["cpp", "hpp", "c", "h", "cmake"],
            ProjectType::Unknown => vec![],
        }
    }
}

/// Git repository information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitInfo {
    /// Current branch
    pub current_branch: String,
    /// Remote URL
    pub remote_url: Option<String>,
    /// Whether there are uncommitted changes
    pub has_uncommitted_changes: bool,
    /// Last commit hash
    pub last_commit_hash: Option<String>,
}

/// User preferences for agent behavior
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    /// Preferred verbosity level
    pub verbosity: VerbosityLevel,
    /// Whether to auto-confirm safe operations
    pub auto_confirm_safe: bool,
    /// Preferred output format
    pub output_format: OutputFormat,
    /// Custom aliases for commands
    pub command_aliases: HashMap<String, String>,
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            verbosity: VerbosityLevel::Normal,
            auto_confirm_safe: true,
            output_format: OutputFormat::Pretty,
            command_aliases: HashMap::new(),
        }
    }
}

/// Verbosity levels for output
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum VerbosityLevel {
    Quiet,
    Normal,
    Verbose,
    Debug,
}

/// Output format preferences
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum OutputFormat {
    /// Pretty formatted output with colors
    Pretty,
    /// Plain text output
    Plain,
    /// JSON output
    Json,
    /// Markdown output
    Markdown,
}

/// Session metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionMetadata {
    /// Session start time
    pub session_start: chrono::DateTime<chrono::Utc>,
    /// Total commands executed
    pub commands_executed: u32,
    /// Total tokens used
    pub tokens_used: u32,
    /// Session ID
    pub session_id: uuid::Uuid,
}

impl Default for SessionMetadata {
    fn default() -> Self {
        Self {
            session_start: chrono::Utc::now(),
            commands_executed: 0,
            tokens_used: 0,
            session_id: uuid::Uuid::new_v4(),
        }
    }
}

/// Context entry for maintaining history
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextEntry {
    /// Timestamp of the entry
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Type of context change
    pub entry_type: ContextEntryType,
    /// Description of the change
    pub description: String,
    /// Associated data
    pub data: serde_json::Value,
}

/// Types of context entries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContextEntryType {
    DirectoryChange,
    ProjectDetection,
    EnvironmentChange,
    ToolExecution,
    UserPreferenceChange,
}

impl AgentContext {
    /// Create a new agent context
    pub fn new(working_directory: PathBuf) -> Result<Self> {
        let environment = std::env::vars().collect();
        let project_info = Self::detect_project_info(&working_directory)?;
        
        Ok(Self {
            working_directory,
            environment,
            project_info,
            user_preferences: UserPreferences::default(),
            session_metadata: SessionMetadata::default(),
            context_history: Vec::new(),
        })
    }

    /// Detect project information from the working directory
    fn detect_project_info(directory: &PathBuf) -> Result<Option<ProjectInfo>> {
        let project_type = ProjectType::detect(directory);
        
        if project_type == ProjectType::Unknown {
            return Ok(None);
        }

        let name = directory
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown")
            .to_string();

        let config_files = Self::find_config_files(directory, &project_type)?;
        let dependencies = Self::detect_dependencies(directory, &project_type)?;
        let git_info = Self::detect_git_info(directory)?;

        Ok(Some(ProjectInfo {
            name,
            project_type,
            root_directory: directory.clone(),
            config_files,
            dependencies,
            git_info,
        }))
    }

    /// Find configuration files for the project type
    fn find_config_files(directory: &PathBuf, project_type: &ProjectType) -> Result<Vec<PathBuf>> {
        let mut config_files = Vec::new();
        
        let config_patterns = match project_type {
            ProjectType::Rust => vec!["Cargo.toml", "Cargo.lock", "rust-toolchain.toml"],
            ProjectType::Node => vec!["package.json", "package-lock.json", "yarn.lock", "tsconfig.json"],
            ProjectType::Python => vec!["requirements.txt", "pyproject.toml", "setup.py", "setup.cfg"],
            ProjectType::Go => vec!["go.mod", "go.sum"],
            ProjectType::Java => vec!["pom.xml", "build.gradle", "gradle.properties"],
            ProjectType::CSharp => vec!["*.csproj", "*.sln", "Directory.Build.props"],
            ProjectType::Cpp => vec!["CMakeLists.txt", "Makefile", "conanfile.txt"],
            ProjectType::Unknown => vec![],
        };

        for pattern in config_patterns {
            let file_path = directory.join(pattern);
            if file_path.exists() {
                config_files.push(file_path);
            }
        }

        Ok(config_files)
    }

    /// Detect dependencies for the project
    fn detect_dependencies(directory: &PathBuf, project_type: &ProjectType) -> Result<Vec<String>> {
        // This is a simplified implementation
        // In a real implementation, you'd parse the actual dependency files
        match project_type {
            ProjectType::Rust => {
                if let Ok(_content) = std::fs::read_to_string(directory.join("Cargo.toml")) {
                    // Parse TOML and extract dependencies
                    // For now, just return a placeholder
                    Ok(vec!["tokio".to_string(), "serde".to_string()])
                } else {
                    Ok(vec![])
                }
            }
            _ => Ok(vec![]),
        }
    }

    /// Detect Git information
    fn detect_git_info(directory: &PathBuf) -> Result<Option<GitInfo>> {
        let git_dir = directory.join(".git");
        if !git_dir.exists() {
            return Ok(None);
        }

        // This is a simplified implementation
        // In a real implementation, you'd use a Git library like git2
        Ok(Some(GitInfo {
            current_branch: "main".to_string(),
            remote_url: None,
            has_uncommitted_changes: false,
            last_commit_hash: None,
        }))
    }

    /// Change the working directory
    pub fn change_directory(&mut self, new_directory: PathBuf) -> Result<()> {
        let old_directory = self.working_directory.clone();
        self.working_directory = new_directory.clone();
        
        // Re-detect project info
        self.project_info = Self::detect_project_info(&new_directory)?;
        
        // Add context entry
        self.add_context_entry(
            ContextEntryType::DirectoryChange,
            format!("Changed directory from {} to {}", 
                   old_directory.display(), new_directory.display()),
            serde_json::json!({
                "old_directory": old_directory,
                "new_directory": new_directory
            })
        );

        debug!("Working directory changed to: {}", new_directory.display());
        Ok(())
    }

    /// Update environment variable
    pub fn set_environment_variable(&mut self, key: String, value: String) {
        self.environment.insert(key.clone(), value.clone());
        
        self.add_context_entry(
            ContextEntryType::EnvironmentChange,
            format!("Set environment variable: {}={}", key, value),
            serde_json::json!({
                "key": key,
                "value": value
            })
        );
    }

    /// Add a context entry
    pub fn add_context_entry(&mut self, entry_type: ContextEntryType, description: String, data: serde_json::Value) {
        let entry = ContextEntry {
            timestamp: chrono::Utc::now(),
            entry_type,
            description,
            data,
        };
        
        self.context_history.push(entry);
        
        // Keep only the last 100 entries
        if self.context_history.len() > 100 {
            self.context_history.remove(0);
        }
    }

    /// Get context summary
    pub fn get_context_summary(&self) -> String {
        let mut summary = format!("Working Directory: {}\n", self.working_directory.display());
        
        if let Some(ref project) = self.project_info {
            summary.push_str(&format!("Project: {} ({})\n", project.name, project.project_type.display_name()));
        }
        
        summary.push_str(&format!("Session: {} commands executed\n", self.session_metadata.commands_executed));
        
        summary
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_project_type_detection() {
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path().to_path_buf();
        
        // Test Rust project detection
        std::fs::write(temp_path.join("Cargo.toml"), "[package]\nname = \"test\"").unwrap();
        assert_eq!(ProjectType::detect(&temp_path), ProjectType::Rust);
    }

    #[test]
    fn test_agent_context_creation() {
        let temp_dir = TempDir::new().unwrap();
        let context = AgentContext::new(temp_dir.path().to_path_buf());
        assert!(context.is_ok());
    }

    #[test]
    fn test_context_entry() {
        let temp_dir = TempDir::new().unwrap();
        let mut context = AgentContext::new(temp_dir.path().to_path_buf()).unwrap();
        
        context.add_context_entry(
            ContextEntryType::ToolExecution,
            "Test entry".to_string(),
            serde_json::json!({"test": "data"})
        );
        
        assert_eq!(context.context_history.len(), 1);
    }
}
