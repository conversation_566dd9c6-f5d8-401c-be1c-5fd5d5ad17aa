use anyhow::{Context, Result};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use schemars::JsonSchema;
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn};

use super::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>ate<PERSON><PERSON>, <PERSON>l<PERSON><PERSON>x<PERSON>, ToolResult, ToolSchema};
use super::schema::{validation, SchemaBuilder};

/// Read file tool
#[derive(Debug)]
pub struct ReadFileToolImpl;

impl ReadFileToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct ReadFileParams {
    /// Path to the file to read
    path: String,
    /// Maximum number of bytes to read (optional)
    max_bytes: Option<usize>,
    /// Line range to read (start, end) - 1-indexed, inclusive (optional)
    line_range: Option<(usize, usize)>,
}

#[async_trait]
impl Tool for ReadFileToolImpl {
    fn name(&self) -> &str {
        "read_file"
    }

    fn description(&self) -> &str {
        "Read the contents of a file. Supports reading entire file, limiting bytes, or specific line ranges."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "path": "README.md"
            }))
            .with_example(serde_json::json!({
                "path": "large_file.txt",
                "max_bytes": 1024
            }))
            .with_example(serde_json::json!({
                "path": "code.rs",
                "line_range": [10, 20]
            }))
            .build::<ReadFileParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::FileSystem
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: ReadFileParams = serde_json::from_value(params)
            .context("Failed to parse read_file parameters")?;

        validation::validate_safe_path(&params.path)?;

        let file_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Reading file: {}", file_path.display());

        if !file_path.exists() {
            return Ok(ToolResult::error(format!("File does not exist: {}", file_path.display())));
        }

        if !file_path.is_file() {
            return Ok(ToolResult::error(format!("Path is not a file: {}", file_path.display())));
        }

        let content = if let Some(max_bytes) = params.max_bytes {
            let mut file = fs::File::open(&file_path).await
                .with_context(|| format!("Failed to open file: {}", file_path.display()))?;
            
            let mut buffer = vec![0; max_bytes];
            let bytes_read = tokio::io::AsyncReadExt::read(&mut file, &mut buffer).await
                .with_context(|| format!("Failed to read file: {}", file_path.display()))?;
            
            buffer.truncate(bytes_read);
            String::from_utf8_lossy(&buffer).to_string()
        } else {
            fs::read_to_string(&file_path).await
                .with_context(|| format!("Failed to read file: {}", file_path.display()))?
        };

        let final_content = if let Some((start, end)) = params.line_range {
            let lines: Vec<&str> = content.lines().collect();
            if start == 0 || start > lines.len() || end == 0 || end > lines.len() || start > end {
                return Ok(ToolResult::error(format!(
                    "Invalid line range: {}-{} (file has {} lines)",
                    start, end, lines.len()
                )));
            }
            
            lines[(start - 1)..end].join("\n")
        } else {
            content
        };

        let metadata = serde_json::json!({
            "file_path": file_path.display().to_string(),
            "size_bytes": final_content.len(),
            "line_count": final_content.lines().count()
        });

        Ok(ToolResult::success(final_content)
            .with_metadata("file_info".to_string(), metadata))
    }
}

/// Write file tool
#[derive(Debug)]
pub struct WriteFileToolImpl;

impl WriteFileToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct WriteFileParams {
    /// Path to the file to write
    path: String,
    /// Content to write to the file
    content: String,
    /// Whether to append to the file instead of overwriting
    append: Option<bool>,
    /// Create parent directories if they don't exist
    create_dirs: Option<bool>,
}

#[async_trait]
impl Tool for WriteFileToolImpl {
    fn name(&self) -> &str {
        "write_file"
    }

    fn description(&self) -> &str {
        "Write content to a file. Can overwrite, append, and create directories as needed."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .requires_confirmation(true)
            .with_example(serde_json::json!({
                "path": "output.txt",
                "content": "Hello, world!"
            }))
            .with_example(serde_json::json!({
                "path": "log.txt",
                "content": "New log entry\n",
                "append": true
            }))
            .build::<WriteFileParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::FileSystem
    }

    fn requires_confirmation(&self) -> bool {
        true
    }

    fn is_safe(&self) -> bool {
        false
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: WriteFileParams = serde_json::from_value(params)
            .context("Failed to parse write_file parameters")?;

        validation::validate_safe_path(&params.path)?;

        let file_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Writing to file: {}", file_path.display());

        // Create parent directories if requested
        if params.create_dirs.unwrap_or(false) {
            if let Some(parent) = file_path.parent() {
                fs::create_dir_all(parent).await
                    .with_context(|| format!("Failed to create directories: {}", parent.display()))?;
            }
        }

        let operation = if params.append.unwrap_or(false) {
            "append"
        } else {
            "write"
        };

        let result = if params.append.unwrap_or(false) {
            fs::write(&file_path, format!("{}{}", 
                fs::read_to_string(&file_path).await.unwrap_or_default(),
                params.content
            )).await
        } else {
            fs::write(&file_path, &params.content).await
        };

        match result {
            Ok(()) => {
                let metadata = serde_json::json!({
                    "file_path": file_path.display().to_string(),
                    "operation": operation,
                    "bytes_written": params.content.len()
                });

                Ok(ToolResult::success(format!(
                    "Successfully {} {} bytes to {}",
                    if params.append.unwrap_or(false) { "appended" } else { "wrote" },
                    params.content.len(),
                    file_path.display()
                )).with_metadata("write_info".to_string(), metadata))
            }
            Err(error) => {
                warn!("Failed to write file {}: {}", file_path.display(), error);
                Ok(ToolResult::error(format!("Failed to write file: {}", error)))
            }
        }
    }
}

/// List directory tool
#[derive(Debug)]
pub struct ListDirectoryToolImpl;

impl ListDirectoryToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct ListDirectoryParams {
    /// Path to the directory to list
    path: String,
    /// Include hidden files and directories
    include_hidden: Option<bool>,
    /// Show detailed information (size, permissions, etc.)
    detailed: Option<bool>,
    /// Recursively list subdirectories
    recursive: Option<bool>,
}

#[async_trait]
impl Tool for ListDirectoryToolImpl {
    fn name(&self) -> &str {
        "list_directory"
    }

    fn description(&self) -> &str {
        "List the contents of a directory. Supports detailed output, hidden files, and recursive listing."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "path": "."
            }))
            .with_example(serde_json::json!({
                "path": "./src",
                "detailed": true,
                "include_hidden": true
            }))
            .build::<ListDirectoryParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::FileSystem
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: ListDirectoryParams = serde_json::from_value(params)
            .context("Failed to parse list_directory parameters")?;

        validation::validate_safe_path(&params.path)?;

        let dir_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Listing directory: {}", dir_path.display());

        if !dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory does not exist: {}", dir_path.display())));
        }

        if !dir_path.is_dir() {
            return Ok(ToolResult::error(format!("Path is not a directory: {}", dir_path.display())));
        }

        let include_hidden = params.include_hidden.unwrap_or(false);
        let detailed = params.detailed.unwrap_or(false);
        let recursive = params.recursive.unwrap_or(false);

        let mut entries = Vec::new();
        let mut total_size = 0u64;

        if recursive {
            let walker = walkdir::WalkDir::new(&dir_path);
            for entry in walker {
                let entry = entry.with_context(|| "Failed to read directory entry")?;
                let path = entry.path();
                
                if !include_hidden && path.file_name()
                    .and_then(|n| n.to_str())
                    .map(|n| n.starts_with('.'))
                    .unwrap_or(false) {
                    continue;
                }

                if detailed {
                    let metadata = entry.metadata().with_context(|| "Failed to read metadata")?;
                    let size = metadata.len();
                    total_size += size;
                    
                    entries.push(format!(
                        "{} {:>10} {}",
                        if metadata.is_dir() { "DIR " } else { "FILE" },
                        size,
                        path.strip_prefix(&dir_path).unwrap_or(path).display()
                    ));
                } else {
                    entries.push(path.strip_prefix(&dir_path).unwrap_or(path).display().to_string());
                }
            }
        } else {
            let mut read_dir = fs::read_dir(&dir_path).await
                .with_context(|| format!("Failed to read directory: {}", dir_path.display()))?;

            while let Some(entry) = read_dir.next_entry().await
                .with_context(|| "Failed to read directory entry")? {

                let file_name = entry.file_name();
                
                if !include_hidden && file_name.to_str()
                    .map(|n| n.starts_with('.'))
                    .unwrap_or(false) {
                    continue;
                }

                if detailed {
                    let metadata = entry.metadata().await
                        .with_context(|| "Failed to read metadata")?;
                    let size = metadata.len();
                    total_size += size;
                    
                    entries.push(format!(
                        "{} {:>10} {}",
                        if metadata.is_dir() { "DIR " } else { "FILE" },
                        size,
                        file_name.to_string_lossy()
                    ));
                } else {
                    entries.push(file_name.to_string_lossy().to_string());
                }
            }
        }

        entries.sort();
        let output = entries.join("\n");

        let metadata = serde_json::json!({
            "directory_path": dir_path.display().to_string(),
            "entry_count": entries.len(),
            "total_size_bytes": total_size,
            "recursive": recursive,
            "detailed": detailed
        });

        Ok(ToolResult::success(output)
            .with_metadata("directory_info".to_string(), metadata))
    }
}

/// Create directory tool
#[derive(Debug)]
pub struct CreateDirectoryToolImpl;

impl CreateDirectoryToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct CreateDirectoryParams {
    /// Path of the directory to create
    path: String,
    /// Create parent directories if they don't exist
    create_parents: Option<bool>,
}

#[async_trait]
impl Tool for CreateDirectoryToolImpl {
    fn name(&self) -> &str {
        "create_directory"
    }

    fn description(&self) -> &str {
        "Create a new directory. Can create parent directories if needed."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "path": "new_folder"
            }))
            .with_example(serde_json::json!({
                "path": "path/to/new/folder",
                "create_parents": true
            }))
            .build::<CreateDirectoryParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::FileSystem
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: CreateDirectoryParams = serde_json::from_value(params)
            .context("Failed to parse create_directory parameters")?;

        validation::validate_safe_path(&params.path)?;

        let dir_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Creating directory: {}", dir_path.display());

        if dir_path.exists() {
            return Ok(ToolResult::error(format!("Directory already exists: {}", dir_path.display())));
        }

        let result = if params.create_parents.unwrap_or(false) {
            fs::create_dir_all(&dir_path).await
        } else {
            fs::create_dir(&dir_path).await
        };

        match result {
            Ok(()) => {
                let metadata = serde_json::json!({
                    "directory_path": dir_path.display().to_string(),
                    "created_parents": params.create_parents.unwrap_or(false)
                });

                Ok(ToolResult::success(format!("Directory created: {}", dir_path.display()))
                    .with_metadata("create_info".to_string(), metadata))
            }
            Err(error) => {
                warn!("Failed to create directory {}: {}", dir_path.display(), error);
                Ok(ToolResult::error(format!("Failed to create directory: {}", error)))
            }
        }
    }
}

/// Delete file tool
#[derive(Debug)]
pub struct DeleteFileToolImpl;

impl DeleteFileToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct DeleteFileParams {
    /// Path to the file or directory to delete
    path: String,
    /// If true, delete directories recursively
    recursive: Option<bool>,
}

#[async_trait]
impl Tool for DeleteFileToolImpl {
    fn name(&self) -> &str {
        "delete_file"
    }

    fn description(&self) -> &str {
        "Delete a file or directory. Use with caution - this operation cannot be undone."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .requires_confirmation(true)
            .with_example(serde_json::json!({
                "path": "temp_file.txt"
            }))
            .with_example(serde_json::json!({
                "path": "temp_directory",
                "recursive": true
            }))
            .build::<DeleteFileParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::FileSystem
    }

    fn requires_confirmation(&self) -> bool {
        true
    }

    fn is_safe(&self) -> bool {
        false
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: DeleteFileParams = serde_json::from_value(params)
            .context("Failed to parse delete_file parameters")?;

        validation::validate_safe_path(&params.path)?;

        let file_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Deleting: {}", file_path.display());

        if !file_path.exists() {
            return Ok(ToolResult::error(format!("Path does not exist: {}", file_path.display())));
        }

        let is_dir = file_path.is_dir();
        let result = if is_dir {
            if params.recursive.unwrap_or(false) {
                fs::remove_dir_all(&file_path).await
            } else {
                fs::remove_dir(&file_path).await
            }
        } else {
            fs::remove_file(&file_path).await
        };

        match result {
            Ok(()) => {
                let metadata = serde_json::json!({
                    "deleted_path": file_path.display().to_string(),
                    "was_directory": is_dir,
                    "recursive": params.recursive.unwrap_or(false)
                });

                Ok(ToolResult::success(format!(
                    "Successfully deleted {}: {}",
                    if is_dir { "directory" } else { "file" },
                    file_path.display()
                )).with_metadata("delete_info".to_string(), metadata))
            }
            Err(error) => {
                warn!("Failed to delete {}: {}", file_path.display(), error);
                Ok(ToolResult::error(format!("Failed to delete: {}", error)))
            }
        }
    }
}
