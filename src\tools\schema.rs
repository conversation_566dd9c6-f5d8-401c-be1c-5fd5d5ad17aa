use anyhow::{Context, Result};
use schemars::{schema::RootSchema, JsonSchema};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;

/// Tool schema definition with JSON Schema validation
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ToolSchema {
    /// Tool name
    pub name: String,
    /// Tool description
    pub description: String,
    /// JSON Schema for parameters
    pub parameters: RootSchema,
    /// Examples of valid parameter sets
    pub examples: Vec<Value>,
    /// Whether the tool requires confirmation
    pub requires_confirmation: bool,
}

impl ToolSchema {
    /// Create a new tool schema
    pub fn new<T: JsonSchema>(
        name: String,
        description: String,
        requires_confirmation: bool,
    ) -> Self {
        let schema = schemars::schema_for!(T);
        
        Self {
            name,
            description,
            parameters: schema,
            examples: Vec::new(),
            requires_confirmation,
        }
    }

    /// Add an example to the schema
    pub fn with_example(mut self, example: Value) -> Self {
        self.examples.push(example);
        self
    }

    /// Add multiple examples to the schema
    pub fn with_examples(mut self, examples: Vec<Value>) -> Self {
        self.examples.extend(examples);
        self
    }

    /// Validate parameters against the schema
    pub fn validate(&self, params: &Value) -> Result<()> {
        // For now, just do basic validation
        // TODO: Implement proper JSON schema validation
        if params.is_null() {
            anyhow::bail!("Parameters cannot be null");
        }

        // Convert schema to JSON for basic type checking
        let schema_json = serde_json::to_value(&self.parameters)
            .context("Failed to serialize schema")?;

        // Basic type checking based on schema
        if let Some(schema_type) = schema_json.get("type") {
            match schema_type.as_str() {
                Some("object") => {
                    if !params.is_object() {
                        anyhow::bail!("Expected object, got {}", params);
                    }
                }
                Some("string") => {
                    if !params.is_string() {
                        anyhow::bail!("Expected string, got {}", params);
                    }
                }
                Some("number") => {
                    if !params.is_number() {
                        anyhow::bail!("Expected number, got {}", params);
                    }
                }
                Some("boolean") => {
                    if !params.is_boolean() {
                        anyhow::bail!("Expected boolean, got {}", params);
                    }
                }
                _ => {} // Allow other types
            }
        }

        Ok(())
    }

    /// Convert to OpenAI function calling format
    pub fn to_openai_function(&self) -> Value {
        let parameters_json = serde_json::to_value(&self.parameters)
            .unwrap_or_else(|_| serde_json::json!({}));

        serde_json::json!({
            "name": self.name,
            "description": self.description,
            "parameters": parameters_json
        })
    }

    /// Convert to Anthropic tool format
    pub fn to_anthropic_tool(&self) -> Value {
        let parameters_json = serde_json::to_value(&self.parameters)
            .unwrap_or_else(|_| serde_json::json!({}));

        serde_json::json!({
            "name": self.name,
            "description": self.description,
            "input_schema": parameters_json
        })
    }
}

/// Schema builder for creating tool schemas with fluent API
pub struct SchemaBuilder {
    name: String,
    description: String,
    requires_confirmation: bool,
    examples: Vec<Value>,
}

impl SchemaBuilder {
    pub fn new(name: impl Into<String>, description: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: description.into(),
            requires_confirmation: false,
            examples: Vec::new(),
        }
    }

    pub fn requires_confirmation(mut self, requires: bool) -> Self {
        self.requires_confirmation = requires;
        self
    }

    pub fn with_example(mut self, example: Value) -> Self {
        self.examples.push(example);
        self
    }

    pub fn build<T: JsonSchema>(self) -> ToolSchema {
        ToolSchema::new::<T>(self.name, self.description, self.requires_confirmation)
            .with_examples(self.examples)
    }
}

/// Common parameter types for tools
#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct FilePathParams {
    /// Path to the file or directory
    pub path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct ShellCommandParams {
    /// Command to execute
    pub command: String,
    /// Working directory (optional)
    pub working_dir: Option<String>,
    /// Environment variables (optional)
    pub env: Option<HashMap<String, String>>,
    /// Timeout in seconds (optional)
    pub timeout: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct SearchParams {
    /// Search query or pattern
    pub query: String,
    /// Path to search in
    pub path: String,
    /// Whether to use regex
    pub regex: Option<bool>,
    /// Case sensitive search
    pub case_sensitive: Option<bool>,
    /// Include hidden files
    pub include_hidden: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct FileWriteParams {
    /// Path to the file
    pub path: String,
    /// Content to write
    pub content: String,
    /// Whether to append or overwrite
    pub append: Option<bool>,
    /// Create directories if they don't exist
    pub create_dirs: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct FileReadParams {
    /// Path to the file
    pub path: String,
    /// Maximum number of bytes to read (optional)
    pub max_bytes: Option<usize>,
    /// Line range to read (start, end) - optional
    pub line_range: Option<(usize, usize)>,
}

/// Utility functions for schema validation
pub mod validation {
    use super::*;

    /// Validate that a path is safe (no directory traversal, etc.)
    pub fn validate_safe_path(path: &str) -> Result<()> {
        let path = std::path::Path::new(path);
        
        // Check for directory traversal attempts
        if path.components().any(|comp| matches!(comp, std::path::Component::ParentDir)) {
            anyhow::bail!("Path contains directory traversal (..)");
        }
        
        // Check for absolute paths that might be dangerous
        if path.is_absolute() {
            let path_str = path.to_string_lossy();
            let dangerous_paths = [
                "/etc", "/usr", "/bin", "/sbin", "/boot", "/sys", "/proc",
                "C:\\Windows", "C:\\Program Files", "C:\\System32",
            ];
            
            for dangerous in &dangerous_paths {
                if path_str.starts_with(dangerous) {
                    anyhow::bail!("Path accesses system directory: {}", dangerous);
                }
            }
        }
        
        Ok(())
    }

    /// Validate that a command is safe to execute
    pub fn validate_safe_command(command: &str) -> Result<()> {
        let dangerous_commands = [
            "rm", "del", "rmdir", "format", "mkfs", "dd", "sudo", "su",
            "chmod", "chown", "passwd", "useradd", "userdel", "groupadd",
            "mount", "umount", "fdisk", "parted", "reboot", "shutdown",
        ];
        
        let command_parts: Vec<&str> = command.split_whitespace().collect();
        if let Some(cmd) = command_parts.first() {
            let cmd_name = std::path::Path::new(cmd)
                .file_name()
                .and_then(|n| n.to_str())
                .unwrap_or(cmd);
            
            if dangerous_commands.contains(&cmd_name) {
                anyhow::bail!("Command '{}' is potentially dangerous", cmd_name);
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(JsonSchema, Serialize, Deserialize)]
    struct TestParams {
        name: String,
        count: u32,
    }

    #[test]
    fn test_schema_creation() {
        let schema = ToolSchema::new::<TestParams>(
            "test_tool".to_string(),
            "A test tool".to_string(),
            false,
        );
        
        assert_eq!(schema.name, "test_tool");
        assert_eq!(schema.description, "A test tool");
        assert!(!schema.requires_confirmation);
    }

    #[test]
    fn test_schema_validation() {
        let schema = ToolSchema::new::<TestParams>(
            "test_tool".to_string(),
            "A test tool".to_string(),
            false,
        );
        
        let valid_params = serde_json::json!({
            "name": "test",
            "count": 42
        });
        
        let invalid_params = serde_json::json!({
            "name": "test"
            // missing count
        });
        
        assert!(schema.validate(&valid_params).is_ok());
        assert!(schema.validate(&invalid_params).is_err());
    }

    #[test]
    fn test_schema_builder() {
        let schema = SchemaBuilder::new("test", "description")
            .requires_confirmation(true)
            .with_example(serde_json::json!({"name": "example", "count": 1}))
            .build::<TestParams>();
        
        assert_eq!(schema.name, "test");
        assert!(schema.requires_confirmation);
        assert_eq!(schema.examples.len(), 1);
    }

    #[test]
    fn test_path_validation() {
        use validation::*;
        
        assert!(validate_safe_path("./safe/path").is_ok());
        assert!(validate_safe_path("safe/path").is_ok());
        assert!(validate_safe_path("../dangerous").is_err());
        assert!(validate_safe_path("/etc/passwd").is_err());
    }

    #[test]
    fn test_command_validation() {
        use validation::*;
        
        assert!(validate_safe_command("ls -la").is_ok());
        assert!(validate_safe_command("echo hello").is_ok());
        assert!(validate_safe_command("rm -rf /").is_err());
        assert!(validate_safe_command("sudo something").is_err());
    }
}
