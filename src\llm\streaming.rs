use anyhow::Result;
use futures::{Stream, StreamExt};
use std::pin::Pin;
use tokio::sync::mpsc;
use tracing::{debug, warn};

use super::{LlmResponse, ResponseChunk, TokenUsage};

/// Streaming response handler that processes LLM response chunks
#[derive(Debug)]
pub struct StreamingResponse {
    /// The accumulated content
    pub content: String,
    /// Whether the stream has finished
    pub is_complete: bool,
    /// Total token usage (if available)
    pub token_usage: Option<TokenUsage>,
    /// Tool calls collected from the stream
    pub tool_calls: Vec<crate::tools::ToolCall>,
    /// Model used for generation
    pub model: String,
    /// Finish reason
    pub finish_reason: Option<String>,
}

impl StreamingResponse {
    /// Create a new streaming response
    pub fn new(model: impl Into<String>) -> Self {
        Self {
            content: String::new(),
            is_complete: false,
            token_usage: None,
            tool_calls: Vec::new(),
            model: model.into(),
            finish_reason: None,
        }
    }

    /// Process a response chunk
    pub fn process_chunk(&mut self, chunk: ResponseChunk) {
        self.content.push_str(&chunk.content);
        
        if let Some(usage) = chunk.token_usage {
            self.token_usage = Some(usage);
        }
        
        if let Some(tool_calls) = chunk.tool_calls {
            self.tool_calls.extend(tool_calls);
        }
        
        if chunk.is_final {
            self.is_complete = true;
        }
    }

    /// Convert to a complete LLM response
    pub fn to_llm_response(self) -> LlmResponse {
        LlmResponse {
            content: self.content,
            token_usage: self.token_usage,
            tool_calls: if self.tool_calls.is_empty() {
                None
            } else {
                Some(self.tool_calls)
            },
            model: self.model,
            finish_reason: self.finish_reason,
        }
    }

    /// Get the current content length
    pub fn content_length(&self) -> usize {
        self.content.len()
    }

    /// Check if there are any tool calls
    pub fn has_tool_calls(&self) -> bool {
        !self.tool_calls.is_empty()
    }
}

/// Handler for processing streaming responses with callbacks
pub struct StreamingResponseHandler {
    /// Callback for each chunk
    on_chunk: Option<Box<dyn Fn(&ResponseChunk) + Send + Sync>>,
    /// Callback for content updates
    on_content: Option<Box<dyn Fn(&str) + Send + Sync>>,
    /// Callback for tool calls
    on_tool_call: Option<Box<dyn Fn(&crate::tools::ToolCall) + Send + Sync>>,
    /// Callback for completion
    on_complete: Option<Box<dyn Fn(&StreamingResponse) + Send + Sync>>,
    /// Callback for errors
    on_error: Option<Box<dyn Fn(&anyhow::Error) + Send + Sync>>,
}

impl StreamingResponseHandler {
    /// Create a new streaming response handler
    pub fn new() -> Self {
        Self {
            on_chunk: None,
            on_content: None,
            on_tool_call: None,
            on_complete: None,
            on_error: None,
        }
    }

    /// Set chunk callback
    pub fn on_chunk<F>(mut self, callback: F) -> Self
    where
        F: Fn(&ResponseChunk) + Send + Sync + 'static,
    {
        self.on_chunk = Some(Box::new(callback));
        self
    }

    /// Set content callback
    pub fn on_content<F>(mut self, callback: F) -> Self
    where
        F: Fn(&str) + Send + Sync + 'static,
    {
        self.on_content = Some(Box::new(callback));
        self
    }

    /// Set tool call callback
    pub fn on_tool_call<F>(mut self, callback: F) -> Self
    where
        F: Fn(&crate::tools::ToolCall) + Send + Sync + 'static,
    {
        self.on_tool_call = Some(Box::new(callback));
        self
    }

    /// Set completion callback
    pub fn on_complete<F>(mut self, callback: F) -> Self
    where
        F: Fn(&StreamingResponse) + Send + Sync + 'static,
    {
        self.on_complete = Some(Box::new(callback));
        self
    }

    /// Set error callback
    pub fn on_error<F>(mut self, callback: F) -> Self
    where
        F: Fn(&anyhow::Error) + Send + Sync + 'static,
    {
        self.on_error = Some(Box::new(callback));
        self
    }

    /// Process a stream of response chunks
    pub async fn process_stream(
        &self,
        mut stream: Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send>>,
        model: impl Into<String>,
    ) -> Result<StreamingResponse> {
        let mut response = StreamingResponse::new(model);
        
        while let Some(chunk_result) = stream.next().await {
            match chunk_result {
                Ok(chunk) => {
                    debug!("Processing chunk: {} chars", chunk.content.len());
                    
                    // Call chunk callback
                    if let Some(ref callback) = self.on_chunk {
                        callback(&chunk);
                    }
                    
                    // Call content callback if there's new content
                    if !chunk.content.is_empty() {
                        if let Some(ref callback) = self.on_content {
                            callback(&chunk.content);
                        }
                    }
                    
                    // Call tool call callback for any tool calls
                    if let Some(ref tool_calls) = chunk.tool_calls {
                        if let Some(ref callback) = self.on_tool_call {
                            for tool_call in tool_calls {
                                callback(tool_call);
                            }
                        }
                    }
                    
                    response.process_chunk(chunk);
                    
                    if response.is_complete {
                        break;
                    }
                }
                Err(error) => {
                    warn!("Stream error: {}", error);
                    if let Some(ref callback) = self.on_error {
                        callback(&error);
                    }
                    return Err(error);
                }
            }
        }
        
        // Call completion callback
        if let Some(ref callback) = self.on_complete {
            callback(&response);
        }
        
        Ok(response)
    }
}

impl Default for StreamingResponseHandler {
    fn default() -> Self {
        Self::new()
    }
}

/// Utility for collecting a stream into a complete response
pub async fn collect_stream(
    stream: Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send>>,
    model: impl Into<String>,
) -> Result<StreamingResponse> {
    let handler = StreamingResponseHandler::new();
    handler.process_stream(stream, model).await
}

/// Utility for processing a stream with real-time display
pub async fn process_stream_with_display(
    stream: Pin<Box<dyn Stream<Item = Result<ResponseChunk>> + Send>>,
    model: impl Into<String>,
) -> Result<StreamingResponse> {
    let handler = StreamingResponseHandler::new()
        .on_content(|content| {
            print!("{}", content);
            use std::io::{self, Write};
            io::stdout().flush().unwrap_or(());
        })
        .on_tool_call(|tool_call| {
            println!("\n🔧 Tool call: {}", tool_call.name);
        })
        .on_complete(|_| {
            println!(); // New line at the end
        })
        .on_error(|error| {
            eprintln!("\n❌ Stream error: {}", error);
        });
    
    handler.process_stream(stream, model).await
}

/// Channel-based streaming response processor
pub struct ChannelStreamProcessor {
    sender: mpsc::UnboundedSender<ResponseChunk>,
    receiver: mpsc::UnboundedReceiver<ResponseChunk>,
}

impl ChannelStreamProcessor {
    /// Create a new channel-based processor
    pub fn new() -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();
        Self { sender, receiver }
    }

    /// Get the sender for pushing chunks
    pub fn sender(&self) -> mpsc::UnboundedSender<ResponseChunk> {
        self.sender.clone()
    }

    /// Process chunks from the receiver
    pub async fn process(mut self, model: impl Into<String>) -> Result<StreamingResponse> {
        let mut response = StreamingResponse::new(model);
        
        while let Some(chunk) = self.receiver.recv().await {
            response.process_chunk(chunk);
            if response.is_complete {
                break;
            }
        }
        
        Ok(response)
    }
}

impl Default for ChannelStreamProcessor {
    fn default() -> Self {
        Self::new()
    }
}

/// Buffered streaming response that accumulates chunks before processing
pub struct BufferedStreamingResponse {
    buffer: Vec<ResponseChunk>,
    buffer_size: usize,
    model: String,
}

impl BufferedStreamingResponse {
    /// Create a new buffered streaming response
    pub fn new(model: impl Into<String>, buffer_size: usize) -> Self {
        Self {
            buffer: Vec::new(),
            buffer_size,
            model: model.into(),
        }
    }

    /// Add a chunk to the buffer
    pub fn add_chunk(&mut self, chunk: ResponseChunk) -> Option<Vec<ResponseChunk>> {
        self.buffer.push(chunk);
        
        if self.buffer.len() >= self.buffer_size || self.buffer.last().map(|c| c.is_final).unwrap_or(false) {
            Some(std::mem::take(&mut self.buffer))
        } else {
            None
        }
    }

    /// Flush remaining chunks
    pub fn flush(&mut self) -> Vec<ResponseChunk> {
        std::mem::take(&mut self.buffer)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use futures::stream;

    #[test]
    fn test_streaming_response() {
        let mut response = StreamingResponse::new("test-model");
        
        let chunk = ResponseChunk {
            content: "Hello".to_string(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        };
        
        response.process_chunk(chunk);
        assert_eq!(response.content, "Hello");
        assert!(!response.is_complete);
        
        let final_chunk = ResponseChunk {
            content: " world!".to_string(),
            is_final: true,
            token_usage: Some(TokenUsage {
                prompt_tokens: 10,
                completion_tokens: 5,
                total_tokens: 15,
            }),
            tool_calls: None,
        };
        
        response.process_chunk(final_chunk);
        assert_eq!(response.content, "Hello world!");
        assert!(response.is_complete);
        assert!(response.token_usage.is_some());
    }

    #[tokio::test]
    async fn test_collect_stream() -> Result<()> {
        let chunks = vec![
            Ok(ResponseChunk {
                content: "Hello".to_string(),
                is_final: false,
                token_usage: None,
                tool_calls: None,
            }),
            Ok(ResponseChunk {
                content: " world!".to_string(),
                is_final: true,
                token_usage: None,
                tool_calls: None,
            }),
        ];
        
        let stream = Box::pin(stream::iter(chunks));
        let response = collect_stream(stream, "test-model").await?;
        
        assert_eq!(response.content, "Hello world!");
        assert!(response.is_complete);
        
        Ok(())
    }

    #[test]
    fn test_buffered_streaming() {
        let mut buffered = BufferedStreamingResponse::new("test", 2);
        
        let chunk1 = ResponseChunk {
            content: "Hello".to_string(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        };
        
        assert!(buffered.add_chunk(chunk1).is_none());
        
        let chunk2 = ResponseChunk {
            content: " world!".to_string(),
            is_final: false,
            token_usage: None,
            tool_calls: None,
        };
        
        let flushed = buffered.add_chunk(chunk2);
        assert!(flushed.is_some());
        assert_eq!(flushed.unwrap().len(), 2);
    }
}
